import { environment } from 'src/environments/environment';
import { LayerOption } from './class/layer-option';

/**
 * 坐标系类型枚举
 */
export enum CoordinateSystem {
  WGS84 = 'WGS84',        // 世界大地坐标系（GPS原生坐标）
  CGCS2000 = 'CGCS2000',  // 国家2000坐标系（天地图，接近WGS84）
  GCJ02 = 'GCJ02',        // 火星坐标系（高德地图、腾讯地图）
  BD09 = 'BD09'           // 百度坐标系（百度地图）
}

/**
 * 底图与坐标系映射关系
 */
export const BaseMapCoordinateMapping: Record<string, CoordinateSystem> = {
  'td_vec': CoordinateSystem.CGCS2000,
  'td_img': CoordinateSystem.CGCS2000,
  'amap_vec': CoordinateSystem.GCJ02,
  'amap_img': CoordinateSystem.GCJ02
};

export const LayerConfigsCN: LayerOption[] = [
    {
        id: 'td_vec',
        name: '天地矢量',
        serverType: 'GROUP',
        selected: true,
        icon: 'assets/map/gl_vec.png',
        childs: [
            {
                id: 'td_vec_0',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=vec_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=vec_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            },
            {
                id: 'td_vec_1',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            }
        ]
    },
    {
        id: 'td_img',
        name: '天地影像',
        serverType: 'GROUP',
        selected: false,
        icon: 'assets/map/gl_img.png',
        childs: [
            {
                id: 'td_img_0',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=img_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=img_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            },
            {
                id: 'td_img1',
                // url: `http://t{0-6}.tianditu.com/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                url: `https://t{0-6}.tianditu.gov.cn/DataServer?T=cva_w&tk=${environment.mapTK}&x={x}&y={y}&l={z}`,
                serverType: 'TILE',
            },
        ]
    },
    {
        id: 'amap_vec', // 图层ID
        name: '高德矢量', // 图层名称
        serverType: 'GROUP', // 图层类型（组）
        selected: false, // 默认是否选中
        icon: 'assets/map/gl_vec.png', // 图层图标（可选）
        childs: [
            {
                id: 'amap_vec_0',
                url: 'https://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}&key=af2dfb6c6340d07e29f469f195acef8f',
                serverType: 'TILE',
            }
        ]
    },
    {
        id: 'amap_img',
        name: '高德影像',
        serverType: 'GROUP',
        selected: false,
        icon: 'assets/map/gl_img.png',
        childs: [
            {
                id: 'amap_img_0',
                url: 'https://webst0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=6&x={x}&y={y}&z={z}&key=af2dfb6c6340d07e29f469f195acef8f',
                serverType: 'TILE',
            },
            {
                id: 'amap_img_1',
                url: 'https://webst0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}&key=af2dfb6c6340d07e29f469f195acef8f',
                serverType: 'TILE',
            },
        ]
    },
];


/**
 * 按坐标系分组的业务图层配置
 * 注意：目前主要支持 CGCS2000 和 GCJ02，WGS84 和 BD09 可通过坐标转换支持
 */
export const BusinessLayerConfigs: Partial<Record<CoordinateSystem, LayerOption[]>> = {
  // 天地图业务图层（CGCS2000坐标系）
  [CoordinateSystem.CGCS2000]: [
    {
      id: 'p_pipe_joint_info',
      name: '管线',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.CGCS2000,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: {
        LAYERS: 'sx:p_pipe_joint_info',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:4490'
      },
    },
    {
      id: 'inspect_point',
      name: '关键点',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.CGCS2000,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: {
        LAYERS: 'sx:inspect_point',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:4490'
      },
    },
    {
      id: 'df_camera',
      name: '摄像头',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.CGCS2000,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: {
        LAYERS: 'sx:df_camera',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:4490'
      },
    },
    {
      id: 'inspect_alarm',
      name: '报警信息',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.CGCS2000,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: {
        LAYERS: 'sx:inspect_alarm',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:4490'
      },
    },
    {
      id: 'inspect_vehicle',
      name: '巡视（车巡）',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.CGCS2000,
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: {
        LAYERS: 'sx:inspect_point',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CQL_FILTER: "inspectionMethod='巡视'",
        CRS: 'EPSG:4490'
      },
    },
    {
      id: 'inspect_person',
      name: '巡查（人巡）',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.CGCS2000,
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx/wms`,
      param: {
        LAYERS: 'sx:inspect_point',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CQL_FILTER: "inspectionMethod='巡查'",
        CRS: 'EPSG:4490'
      },
    }
  ],

  // 高德地图业务图层（GCJ-02坐标系）
  [CoordinateSystem.GCJ02]: [
    {
      id: 'p_pipe_joint_info_gcj02',
      name: '管线',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.GCJ02,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx_gcj02/wms`,
      param: {
        LAYERS: 'sx_gcj02:p_pipe_joint_info',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:3857'
      },
    },
    {
      id: 'inspect_point_gcj02',
      name: '关键点',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.GCJ02,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx_gcj02/wms`,
      param: {
        LAYERS: 'sx_gcj02:inspect_point',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:3857'
      },
    },
    {
      id: 'df_camera_gcj02',
      name: '摄像头',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.GCJ02,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx_gcj02/wms`,
      param: {
        LAYERS: 'sx_gcj02:df_camera',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:3857'
      },
    },
    {
      id: 'inspect_alarm_gcj02',
      name: '报警信息',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.GCJ02,
      selected: true,
      url: `${environment.api.mapUrl}/geoserver/sx_gcj02/wms`,
      param: {
        LAYERS: 'sx_gcj02:inspect_alarm',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CRS: 'EPSG:3857'
      },
    },
    {
      id: 'inspect_vehicle_gcj02',
      name: '巡视（车巡）',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.GCJ02,
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx_gcj02/wms`,
      param: {
        LAYERS: 'sx_gcj02:inspect_point',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CQL_FILTER: "inspectionMethod='巡视'",
        CRS: 'EPSG:3857'
      },
    },
    {
      id: 'inspect_person_gcj02',
      name: '巡查（人巡）',
      serverType: 'WMS' as const,
      coordinateSystem: CoordinateSystem.GCJ02,
      selected: false,
      url: `${environment.api.mapUrl}/geoserver/sx_gcj02/wms`,
      param: {
        LAYERS: 'sx_gcj02:inspect_point',
        VERSION: '1.1.0',
        FORMAT: 'image/png',
        CQL_FILTER: "inspectionMethod='巡查'",
        CRS: 'EPSG:3857'
      },
    }
  ]
};

/**
 * 向后兼容的业务图层配置（默认使用CGCS2000）
 */
export const BusinessLayerConfigsCN: LayerOption[] = BusinessLayerConfigs[CoordinateSystem.CGCS2000] as LayerOption[];
