/**
 * coordtransform 库的 TypeScript 类型声明
 * 提供百度坐标（BD09）、国测局坐标（火星坐标，GCJ02）、和WGS84坐标系之间的转换
 */
declare module 'coordtransform' {
  /**
   * 坐标点类型 [经度, 纬度]
   */
  type CoordinatePoint = [number, number];

  /**
   * 百度坐标转国测局坐标（火星坐标）
   * @param lng 经度
   * @param lat 纬度
   * @returns [经度, 纬度]
   */
  export function bd09togcj02(lng: number, lat: number): CoordinatePoint;

  /**
   * 国测局坐标（火星坐标）转百度坐标
   * @param lng 经度
   * @param lat 纬度
   * @returns [经度, 纬度]
   */
  export function gcj02tobd09(lng: number, lat: number): CoordinatePoint;

  /**
   * WGS84坐标转国测局坐标（火星坐标）
   * @param lng 经度
   * @param lat 纬度
   * @returns [经度, 纬度]
   */
  export function wgs84togcj02(lng: number, lat: number): CoordinatePoint;

  /**
   * 国测局坐标（火星坐标）转WGS84坐标
   * @param lng 经度
   * @param lat 纬度
   * @returns [经度, 纬度]
   */
  export function gcj02towgs84(lng: number, lat: number): CoordinatePoint;
}
