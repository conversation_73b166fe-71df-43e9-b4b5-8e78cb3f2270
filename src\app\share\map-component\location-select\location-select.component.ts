import { AfterViewInit, Component, HostListener, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Geolocation } from '@ionic-native/geolocation/ngx';
import { Coordinate } from 'ol/coordinate';
import { MapComponent } from '../map/map.component';
import { KeyPointInfo } from 'src/app/execut/class/key-point';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DetailsMode } from 'src/app/@core/base/environment';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { ExecutService } from 'src/app/execut/execut.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { CoordinateSystemService, CoordinateTransformResult } from '../service/coordinate-system.service';
import { CoordinateSystem } from '../map/layer.config';

@Component({
  selector: 'app-location-select',
  templateUrl: './location-select.component.html',
  styleUrls: ['./location-select.component.scss'],
})
export class LocationSelectComponent implements AfterViewInit, OnInit, OnDestroy {
  @ViewChild('ostMap', { static: false }) ostMap!: MapComponent;
  @Input() coordinate: Coordinate = [116.4047470, 39.9069387];
  @Input() taskCode!: string;
  @Input() modelInfo: KeyPointInfo = new KeyPointInfo();
  @Input() modelMode!: DetailsMode;

  layerId: string[] = ['inspect_point'];
  DetailsMode = DetailsMode;

  isItRaining = false;
  showMap = true;
  infoFormGroup!: FormGroup;

  // 步骤控制
  currentStep: 'location' | 'info' = 'location';
  showMask: boolean = true;
  isCollecting: boolean = false;
  countdown: number = 5;
  locationConfirmed: boolean = false;

  private collectedLocations: Array<{
    coordinate: Coordinate,
    accuracy: number,
    originalCoordinate?: Coordinate,
    coordinateSystem?: CoordinateSystem,
    transformResult?: CoordinateTransformResult | null
  }> = [];
  private bestLocation: {
    coordinate: Coordinate,
    accuracy: number,
    originalCoordinate?: Coordinate,
    coordinateSystem?: CoordinateSystem,
    transformResult?: CoordinateTransformResult | null
  } | null = null;

  // 偏差值状态管理
  accuracyStatus: 'good' | 'poor' | 'unknown' = 'unknown';
  showAccuracyTip: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private modalController: ModalController, private fb: FormBuilder,
    private userSer: UserInfoService, private netSer: ExecutService,
    private toastSer: ToastService, private geolocation: Geolocation,
    private coordinateSystemService: CoordinateSystemService
  ) { }

  ngOnInit(): void {
    this.initializeData();
    this.createForm();
  }

  ngAfterViewInit(): void {
    this.initCenterPoint();
  }

  /**
   * 初始化数据
   */
  private initializeData(): void {
    if (this.modelMode === DetailsMode.ADD) {
      this.modelInfo.depCode = this.userSer.depCode;
      // 新增模式下，从位置选择步骤开始
      this.currentStep = 'location';
      this.showMask = true;
      this.locationConfirmed = false;
    } else {
      // 编辑模式下，直接跳到信息填写步骤
      this.coordinate = this.modelInfo.pointGeom;
      this.isItRaining = this.convertStringToBoolean(this.modelInfo.isItRaining);
      this.currentStep = 'info';
      this.showMask = false;
      this.locationConfirmed = true;
    }
  }

  /**
   * 创建表单
   */
  private createForm(): void {
    this.infoFormGroup = this.fb.group({
      depCode: [this.modelInfo.depCode, [Validators.required]],
      depName: [this.modelInfo.depName],
      pointName: [this.modelInfo.pointName,[Validators.required]],
      inspectionMethod: [this.modelInfo.inspectionMethod,[Validators.required]],
      bufferRange: [this.modelInfo.bufferRange],
      isItRaining: [this.modelInfo.isItRaining],
      taskCode: [this.modelInfo.taskCode || this.taskCode],
    });
  }

  /**
   * 将字符串转换为布尔值
   */
  private convertStringToBoolean(value: string): boolean {
    return value === '是';
  }

  /**
   * 将布尔值转换为字符串
   */
  private convertBooleanToString(value: boolean): string {
    return value ? '是' : '否';
  }

  /**
   * 地图初始化中心点（支持坐标系转换）
   */
  private initCenterPoint(): void {
    if (this.isValidCoordinate(this.coordinate)) {
      // 获取当前地图坐标系
      const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();

      // 如果是编辑模式且有坐标系信息，检查是否需要转换
      let finalCoordinate = this.coordinate;

      if (this.modelMode === DetailsMode.EDITE && (this.modelInfo as any).coordinateSystem) {
        const sourceCRS = (this.modelInfo as any).coordinateSystem as CoordinateSystem;

        // 如果数据坐标系与当前地图坐标系不同，进行转换
        if (sourceCRS !== currentCRS) {
          const transformResult = this.coordinateSystemService.transformCoordinate(
            this.coordinate,
            sourceCRS,
            currentCRS
          );
          finalCoordinate = transformResult.coordinate;

          console.log('🔄 编辑模式坐标转换:', {
            原始坐标: this.coordinate,
            源坐标系: sourceCRS,
            目标坐标系: currentCRS,
            转换后坐标: finalCoordinate
          });
        }
      }

      this.coordinate = finalCoordinate;
      this.ostMap.setCurrentLocation(finalCoordinate);
    } else {
      // 如果没有有效坐标，不进行定位，等待用户手动采集
      console.log('🎯 没有有效坐标，等待用户手动采集');
    }
  }

  /**
   * 检查坐标是否有效
   * @param coordinate 坐标
   * @returns 是否有效
   */
  private isValidCoordinate(coordinate: any): boolean {
    return coordinate &&
      !(Array.isArray(coordinate) && coordinate[0] === 0 && coordinate[1] === 0);
  }

  /**
   * 切换地图/表单视图
   */
  segmentChanged(ev: any): void {
    const segment = ev.detail.value;
    if (segment === 'map') {
      this.currentStep = 'location';
      this.showMap = true;
    } else if (segment === 'basic') {
      // 只有在位置已确认的情况下才能切换到基础信息
      if (this.locationConfirmed) {
        this.currentStep = 'info';
        this.showMap = false;
      } else {
        // 如果位置未确认，提示用户先确认位置
        this.toastSer.presentToast('请先确认位置信息', 'warning');
        // 重置segment到map，使用更可靠的方式
        this.resetSegmentToMap();
      }
    }
  }

  /**
   * 重置segment到地图选项
   */
  private resetSegmentToMap(): void {
    setTimeout(() => {
      const segment = document.querySelector('ion-segment') as any;
      if (segment) {
        segment.value = 'map';
      }
    }, 100);
  }

  /**
   * 部门值改变
   */
  valueChange(ev: { name: string }): void {
    this.infoFormGroup.get('depName')?.setValue(ev.name);
  }

  /**
   * 确定提交
   */
  onConfirm(): void {
    if (this.currentStep === 'location') {
      // 位置选择步骤，确认位置
      this.confirmLocation();
    } else if (this.currentStep === 'info') {
      // 基础信息步骤，提交表单
      if (!this.infoFormGroup.valid) {
        this.toastSer.presentToast('请填写必填项', 'warning');
        return;
      }
      const formData = this.prepareFormData();
      this.onSubmit(formData);
    }
  }

  /**
   * 确认位置
   */
  confirmLocation(): void {
    if (!this.isValidCoordinate(this.coordinate)) {
      this.toastSer.presentToast('请先采集位置信息', 'warning');
      return;
    }

    this.locationConfirmed = true;
    this.currentStep = 'info';
    this.showMap = false;

    // 自动切换到基础信息tab
    this.switchToBasicSegment();

    this.toastSer.presentToast('位置确认成功，请填写基础信息', 'success');
  }

  /**
   * 切换到基础信息segment
   */
  private switchToBasicSegment(): void {
    setTimeout(() => {
      const segment = document.querySelector('ion-segment') as any;
      if (segment) {
        segment.value = 'basic';
      }
    }, 100);
  }

  /**
   * 准备提交数据（包含坐标系信息）
   */
  private prepareFormData(): KeyPointInfo {
    const formData = Object.assign(this.modelInfo, this.infoFormGroup.value);
    formData.isItRaining = this.convertBooleanToString(this.isItRaining);

    // 获取当前地图中心点坐标
    const centerCoordinate = this.ostMap.view.getCenter();
    formData.pointGeom = centerCoordinate;

    // 添加坐标系信息
    const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
    (formData as any).coordinateSystem = currentCRS;

    console.log('📝 准备提交关键点数据:', {
      坐标: centerCoordinate,
      坐标系: currentCRS,
      关键点名称: formData.pointName
    });

    return formData;
  }

  /**
   * 保存表单
   */
  private async onSubmit(formData: KeyPointInfo): Promise<void> {
    const operate = this.modelMode === DetailsMode.ADD
      ? this.netSer.addKeyPoint(formData)
      : this.netSer.updateKeyPoint(formData);

    try {
      const res = await operate.pipe(takeUntil(this.destroy$)).toPromise();
      if (res?.code === 0) {
        this.toastSer.presentToast('操作成功', 'success');
        await this.modalController.dismiss({ centerCoord: formData.pointGeom }, 'confirm');
      } else {
        this.toastSer.presentToast(res?.msg || '操作失败', 'danger');
      }
    } catch (error) {
      console.error('Error submitting form data:', error);
      this.toastSer.presentToast('网络错误，请稍后重试', 'danger');
    }
  }

  /**
   * 回退
   */
  goBack(): void {
    this.modalController.dismiss();
  }

  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton(event: CustomEvent): void {
    event.detail.register(100, async () => {
      event.stopImmediatePropagation();
      await this.modalController.dismiss();
    });
  }

  /**
   * 开始采集按钮点击
   */
  startCollect(): void {
    this.isCollecting = true;
    this.countdown = 5;
    this.collectedLocations = [];
    this.bestLocation = null;
    this.resetAccuracyStatus(); // 重置精度状态
    this.startLocationCollection();
    this.countdownTick();
  }

  /**
   * 开始定位采集 - 使用Geolocation插件
   */
  private startLocationCollection(): void {
    console.log('🚀 开始关键点定位采集流程');

    // 立即开始第一次定位
    this.collectLocation();

    // 每2秒采集一次定位，持续5秒（总共3次定位）
    const collectionInterval = setInterval(() => {
      if (this.isCollecting) {
        this.collectLocation();
      } else {
        clearInterval(collectionInterval);
      }
    }, 2000);
  }

  /**
   * 采集定位信息 - 使用Geolocation插件避免与巡检轨迹冲突
   */
  private async collectLocation(): Promise<void> {
    try {
      console.log('🎯 开始关键点定位采集（使用Geolocation插件）');

      // 直接使用Geolocation插件进行定位，避免与巡检轨迹的BackgroundGeolocation冲突
      const options = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      };

      const position = await this.geolocation.getCurrentPosition(options);
      const coords = position.coords;

      console.log('📍 关键点定位结果:', {
        accuracy: coords.accuracy,
        longitude: coords.longitude,
        latitude: coords.latitude
      });

      // 转换为地图坐标格式
      const coordinate: Coordinate = [coords.longitude, coords.latitude];

      // 处理定位结果
      this.handleLocationResult(coordinate, coords.accuracy);

    } catch (error) {
      console.error('❌ 关键点定位采集失败:', error);
      // 可以在这里添加错误处理，比如显示提示信息
    }
  }

  /**
   * 处理定位结果（支持坐标系转换）
   */
  private handleLocationResult(coordinate: Coordinate, accuracy: number): void {
    console.log('🎯 处理定位结果:', { coordinate, accuracy });

    // 获取当前地图使用的坐标系
    const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
    console.log('🗺️ 当前地图坐标系:', currentCRS);

    // GPS通常返回WGS84坐标，需要根据当前地图坐标系进行转换
    let finalCoordinate = coordinate;
    let transformResult: CoordinateTransformResult | null = null;

    // 如果当前使用的是GCJ02坐标系（高德地图），需要进行坐标转换
    if (currentCRS === CoordinateSystem.GCJ02) {
      transformResult = this.coordinateSystemService.transformCoordinate(
        coordinate,
        CoordinateSystem.WGS84, // GPS坐标是WGS84坐标系
        CoordinateSystem.GCJ02
      );
      finalCoordinate = transformResult.coordinate;
      console.log('🔄 坐标转换:', {
        原始坐标: coordinate,
        转换后坐标: finalCoordinate,
        转换精度: transformResult.accuracy
      });
    }

    const location = {
      coordinate: finalCoordinate,
      accuracy,
      originalCoordinate: coordinate, // 保存原始坐标
      coordinateSystem: currentCRS,
      transformResult
    };

    this.collectedLocations.push(location);
    console.log('📍 已采集定位数量:', this.collectedLocations.length);

    // 更新最佳定位点（精度最高）
    if (!this.bestLocation || accuracy < this.bestLocation.accuracy) {
      this.bestLocation = location;
      console.log('⭐ 更新最佳定位点:', {
        coordinate: finalCoordinate,
        accuracy,
        coordinateSystem: currentCRS
      });
    } else {
      console.log('📊 当前定位精度不如最佳定位点，跳过');
    }
  }



  /**
   * 倒计时递减
   */
  private countdownTick(): void {
    if (this.countdown > 1) {
      setTimeout(() => {
        this.countdown--;
        this.countdownTick();
      }, 1000);
    } else {
      this.finishCollection();
    }
  }

  /**
   * 完成采集（支持坐标系转换）
   */
  private finishCollection(): void {
    this.isCollecting = false;

    if (this.bestLocation) {
      // 使用最佳定位点（已经过坐标系转换）
      this.coordinate = this.bestLocation.coordinate;
      this.ostMap.setCurrentLocation(this.coordinate, this.bestLocation.accuracy);

      const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();
      console.log('✅ 采集完成，最佳定位点:', {
        坐标: this.bestLocation.coordinate,
        精度: this.bestLocation.accuracy,
        坐标系: currentCRS,
        原始坐标: this.bestLocation.originalCoordinate,
        是否转换: this.bestLocation.transformResult ? '是' : '否'
      });

      // 评估偏差值并设置状态
      this.evaluateAccuracy(this.bestLocation.accuracy);

      // 采集成功，关闭遮罩，显示位置确认界面
      this.showMask = false;

      // 根据偏差值给出不同的提示
      if (this.accuracyStatus === 'good') {
        const crsName = this.coordinateSystemService.getCoordinateSystemDisplayName(currentCRS);
        this.toastSer.presentToast(`位置采集成功（${crsName}），精度良好，请确认位置`, 'success');
      } else {
        this.showAccuracyTip = true;
      }
    } else {
      // 采集失败，重新显示遮罩让用户重试
      this.toastSer.presentToast('定位采集失败，请重试', 'danger');
      console.error('❌ 定位采集失败，未获取到有效定位');
      // 重置状态，允许重新采集
      this.countdown = 5;
      this.collectedLocations = [];
      this.bestLocation = null;
      this.resetAccuracyStatus();
    }
  }

  /**
   * 返回位置选择步骤
   */
  backToLocationStep(): void {
    this.currentStep = 'location';
    this.showMap = true;
    this.locationConfirmed = false;
    this.resetAccuracyStatus(); // 重置精度状态

    // 自动切换到地图tab
    this.resetSegmentToMap();
  }

  /**
   * 评估定位精度
   * @param accuracy 精度值（米）
   */
  private evaluateAccuracy(accuracy: number): void {
    // 调整精度标准：10米以内为良好，超过10米提示偏差较大
    if (accuracy <= 10) {
      this.accuracyStatus = 'good';
      this.showAccuracyTip = false;
    } else {
      this.accuracyStatus = 'poor';
      this.showAccuracyTip = true;
    }
  }

  /**
   * 重置精度状态
   */
  private resetAccuracyStatus(): void {
    this.accuracyStatus = 'unknown';
    this.showAccuracyTip = false;
  }

  /**
   * 关闭精度提示
   */
  closeAccuracyTip(): void {
    this.showAccuracyTip = false;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}