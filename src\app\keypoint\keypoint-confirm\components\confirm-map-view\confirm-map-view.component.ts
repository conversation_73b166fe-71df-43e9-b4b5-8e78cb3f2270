import { Component, Input, OnInit, OnChanges, SimpleChanges, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { UnconfirmedKeyPoint } from '../../keypoint-confirm.service';
import { MapComponent } from '../../../../share/map-component/map/map.component';
import { CoordinateSystemService } from '../../../../share/map-component/service/coordinate-system.service';
import { CoordinateSystem } from '../../../../share/map-component/map/layer.config';

@Component({
  selector: 'app-confirm-map-view',
  templateUrl: './confirm-map-view.component.html',
  styleUrls: ['./confirm-map-view.component.scss']
})
export class ConfirmMapViewComponent implements OnInit, OnChanges {
  @ViewChild('mapComponent') mapComponent: MapComponent;
  @ViewChild('mapContainer') mapContainer: ElementRef;

  @Input() points: UnconfirmedKeyPoint[] = [];

  mapReady = false;
  layerIds: string[] = ['p_pipe_joint_info', 'unconfirmed_points'];

  constructor(
    private cdr: ChangeDetectorRef,
    private coordinateSystemService: CoordinateSystemService
  ) { }

  ngOnInit() {
    // 地图加载完成会通过mapLoaded事件触发onMapReady方法
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['points'] && this.mapReady && this.points.length > 0) {
      this.updateMapPoints();
      // 当关键点数据更新时，自动定位到所有关键点范围
      setTimeout(() => {
        this.fitToAllPoints();
      }, 300);
    }
  }
  /**
   * 地图准备就绪事件
   */
  onMapReady() {
    this.mapReady = true;
    this.updateMapPoints();
    // 自动定位到所有待确认关键点的范围
    setTimeout(() => {
      this.fitToAllPoints();
    }, 500);
    this.cdr.detectChanges();
  }

  /**
   * 更新地图上的关键点显示（支持坐标系转换）
   */
  private updateMapPoints() {
    if (this.mapComponent && this.mapReady && this.points) {
      // 获取当前地图坐标系
      const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();

      // 转换数据格式以适配地图组件
      const mapPoints = this.points.map(point => {
          let longitude = point.longitude;
          let latitude = point.latitude;

          // 如果没有直接的坐标信息，从geom字段解析
          if (!longitude || !latitude) {
            try {
              const geom = JSON.parse(point.geom);
              if (geom.type === 'Point' && geom.coordinates && geom.coordinates.length >= 2) {
                longitude = geom.coordinates[0];
                latitude = geom.coordinates[1];
              }
            } catch (error) {
              console.warn('解析关键点坐标失败:', point.pointCode, error);
              return null;
            }
          }

          // 处理坐标系转换
          let finalCoordinate: [number, number] = [longitude, latitude];
          const keyPointCRS = (point as any).coordinateSystem as CoordinateSystem;

          if (keyPointCRS && keyPointCRS !== currentCRS) {
            // 需要进行坐标系转换
            const transformResult = this.coordinateSystemService.transformCoordinate(
              finalCoordinate,
              keyPointCRS,
              currentCRS
            );
            finalCoordinate = transformResult.coordinate as [number, number];

            console.log('🔄 地图显示坐标转换:', {
              关键点名称: point.pointName,
              原始坐标: [longitude, latitude],
              源坐标系: keyPointCRS,
              目标坐标系: currentCRS,
              转换后坐标: finalCoordinate
            });
          } else if (!keyPointCRS) {
            // 历史数据没有坐标系信息，假设为CGCS2000
            const assumedCRS = CoordinateSystem.CGCS2000;
            if (assumedCRS !== currentCRS) {
              const transformResult = this.coordinateSystemService.transformCoordinate(
                finalCoordinate,
                assumedCRS,
                currentCRS
              );
              finalCoordinate = transformResult.coordinate as [number, number];
            }
          }

        // KeyPointRenderer期望的数据格式
        const mapPoint = {
          id: point.pointCode,
          pointName: point.pointName,
          point: JSON.stringify(finalCoordinate), // 使用转换后的坐标
          state: '未确认', // 统一显示为未确认状态
          bufferTrans: point.bufferTrans || 0, // 缓冲范围
          ...point
        };

        return mapPoint;
      }).filter(point => point !== null); // 过滤掉无效的点

      // 调用地图组件的setKeyPoints方法
      this.mapComponent.setKeyPoints(mapPoints);

      console.log('🗺️ 地图显示关键点:', {
        总数: mapPoints.length,
        坐标系: currentCRS
      });
    }
  }

  /**
   * 缩放到所有关键点（支持坐标系转换）
   */
  fitToAllPoints() {
    if (this.mapComponent && this.mapComponent.map && this.points.length > 0) {
      try {
        // 获取当前地图坐标系
        const currentCRS = this.coordinateSystemService.getCurrentCoordinateSystemValue();

        // 计算所有关键点的边界范围
        const coordinates: number[][] = [];

        this.points.forEach(point => {
          let longitude = point.longitude;
          let latitude = point.latitude;

          // 如果没有直接的坐标信息，从geom字段解析
          if (!longitude || !latitude) {
            try {
              const geom = JSON.parse(point.geom);
              if (geom.type === 'Point' && geom.coordinates && geom.coordinates.length >= 2) {
                longitude = geom.coordinates[0];
                latitude = geom.coordinates[1];
              }
            } catch (error) {
              console.warn('解析关键点坐标失败:', point.pointCode, error);
              return;
            }
          }

          if (longitude && latitude && longitude !== 0 && latitude !== 0) {
            // 处理坐标系转换
            let finalCoordinate: [number, number] = [longitude, latitude];
            const keyPointCRS = (point as any).coordinateSystem as CoordinateSystem;

            if (keyPointCRS && keyPointCRS !== currentCRS) {
              // 需要进行坐标系转换
              const transformResult = this.coordinateSystemService.transformCoordinate(
                finalCoordinate,
                keyPointCRS,
                currentCRS
              );
              finalCoordinate = transformResult.coordinate as [number, number];
            } else if (!keyPointCRS) {
              // 历史数据没有坐标系信息，假设为CGCS2000
              const assumedCRS = CoordinateSystem.CGCS2000;
              if (assumedCRS !== currentCRS) {
                const transformResult = this.coordinateSystemService.transformCoordinate(
                  finalCoordinate,
                  assumedCRS,
                  currentCRS
                );
                finalCoordinate = transformResult.coordinate as [number, number];
              }
            }

            coordinates.push(finalCoordinate);
          }
        });
        
        if (coordinates.length > 0) {
          // 计算边界范围
          const lons = coordinates.map(coord => coord[0]);
          const lats = coordinates.map(coord => coord[1]);
          const minLon = Math.min(...lons);
          const maxLon = Math.max(...lons);
          const minLat = Math.min(...lats);
          const maxLat = Math.max(...lats);
          
          // 添加一些边距
          const padding = 0.001; // 约100米的边距
          const extent = [
            minLon - padding,
            minLat - padding,
            maxLon + padding,
            maxLat + padding
          ];
          

          
          // 使用地图视图的fit方法缩放到计算的范围
          this.mapComponent.map.getView().fit(extent, {
            duration: 1000,
            padding: [50, 50, 50, 50],
            maxZoom: 18
          });
        }
      } catch (error) {
        console.error('缩放到关键点范围失败:', error);
      }
    }
  }


}