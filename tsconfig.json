{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "skipLibCheck": true, "moduleResolution": "node", "importHelpers": true, "target": "es2015", "module": "es2020", "lib": ["es2018", "dom"], "typeRoots": ["node_modules/@types", "src/types"]}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}